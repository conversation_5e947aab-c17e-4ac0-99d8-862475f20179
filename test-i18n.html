<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>I18n Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>多语言功能测试</h1>
    
    <div class="test-section">
        <h2>当前页面信息</h2>
        <p>当前路径: <span id="current-path"></span></p>
        <p>检测到的语言: <span id="detected-lang"></span></p>
        <p>翻译文件路径: <span id="translation-path"></span></p>
    </div>

    <div class="test-section">
        <h2>翻译测试</h2>
        <p data-i18n-key="logo">Read Today</p>
        <p data-i18n-key="authTitle">Welcome to Read Today</p>
        <p data-i18n-key="authSubtitle">A simple and private read-later tool.</p>
        <button data-i18n-key="loginButton">Sign in with Google</button>
    </div>

    <div class="test-section">
        <h2>语言切换测试</h2>
        <button onclick="testLanguageSwitch()">测试语言切换</button>
        <p id="switch-result"></p>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 复制 i18n.js 的逻辑进行测试
        function detectLanguageAndPath() {
            const currentPath = window.location.pathname;
            const lang = currentPath.startsWith('/zh') || currentPath.startsWith('/zh/') ? 'zh' : 'en';
            
            let translationPath;
            if (currentPath.startsWith('/zh/')) {
                translationPath = `../${lang}.json`;
            } else {
                translationPath = `${lang}.json`;
            }
            
            return { currentPath, lang, translationPath };
        }

        async function testTranslations() {
            const { currentPath, lang, translationPath } = detectLanguageAndPath();
            
            document.getElementById('current-path').textContent = currentPath;
            document.getElementById('detected-lang').textContent = lang;
            document.getElementById('translation-path').textContent = translationPath;
            
            const results = [];
            
            try {
                const response = await fetch(translationPath);
                if (response.ok) {
                    const translations = await response.json();
                    results.push(`<p class="success">✓ 翻译文件加载成功</p>`);
                    
                    // 测试翻译应用
                    document.querySelectorAll('[data-i18n-key]').forEach(element => {
                        const key = element.getAttribute('data-i18n-key');
                        if (translations[key]) {
                            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                                element.placeholder = translations[key];
                            } else {
                                element.textContent = translations[key];
                            }
                            results.push(`<p class="success">✓ 翻译应用成功: ${key} -> ${translations[key]}</p>`);
                        } else {
                            results.push(`<p class="error">✗ 缺少翻译: ${key}</p>`);
                        }
                    });
                } else {
                    results.push(`<p class="error">✗ 翻译文件加载失败: ${response.status}</p>`);
                }
            } catch (error) {
                results.push(`<p class="error">✗ 错误: ${error.message}</p>`);
            }
            
            document.getElementById('test-results').innerHTML = results.join('');
        }

        function testLanguageSwitch() {
            const currentPath = window.location.pathname;
            const isZhPage = currentPath.startsWith('/zh/') || currentPath === '/zh';
            
            let newPath;
            if (isZhPage) {
                if (currentPath === '/zh' || currentPath === '/zh/') {
                    newPath = '/';
                } else {
                    newPath = currentPath.replace('/zh/', '/');
                }
            } else {
                if (currentPath === '/') {
                    newPath = '/zh/';
                } else {
                    newPath = '/zh' + currentPath;
                }
            }
            
            document.getElementById('switch-result').innerHTML = 
                `<p>当前路径: ${currentPath}</p><p>切换后路径: ${newPath}</p>`;
        }

        // 页面加载时运行测试
        document.addEventListener('DOMContentLoaded', testTranslations);
    </script>
</body>
</html>
