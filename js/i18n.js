async function applyTranslations() {
    const currentPath = window.location.pathname;
    const lang = currentPath.startsWith('/zh') || currentPath.startsWith('/zh/') ? 'zh' : 'en';

    try {
        // Determine the correct path for translation files
        let translationPath;
        if (currentPath.startsWith('/zh/')) {
            // We're in a subdirectory, need to go up one level
            translationPath = `../${lang}.json`;
        } else {
            // We're in the root directory
            translationPath = `${lang}.json`;
        }

        const response = await fetch(translationPath);
        const translations = await response.json();

        document.querySelectorAll('[data-i18n-key]').forEach(element => {
            const key = element.getAttribute('data-i18n-key');
            if (translations[key]) {
                // Handle different element types
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = translations[key];
                } else {
                    element.textContent = translations[key];
                }
            }
        });

        // Special handling for document title
        if (translations.pageTitle) {
            document.title = translations.pageTitle;
        }

    } catch (error) {
        console.error('Error loading or applying translations:', error);
    }
}

document.addEventListener('DOMContentLoaded', applyTranslations);
