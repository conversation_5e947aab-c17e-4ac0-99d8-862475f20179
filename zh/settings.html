<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n-key="settingsTitle">Settings - Read Today</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header class="app-header">
        <div class="container header-container">
            <a href="/" class="logo-link" data-i18n-key="settingsBackButton">Back to Home</a>
            <button id="theme-switcher" title="切换主题"></button>
            <button id="lang-switcher-btn" title="切换语言"></button>
        </div>
    </header>
    <main id="app">
        <div class="container">
            <h2 class="page-title" data-i18n-key="settingsTitle">Settings</h2>
            <div class="setting-card">
                <h3 data-i18n-key="bookmarkletTitle">Bookmarklet for Easy Saving</h3>
                <p class="setting-description" data-i18n-key="bookmarkletSubtitle">Drag the blue pill below to your browser's bookmarks bar...</p>
                <a id="bookmarklet-link" href="#" class="bookmarklet" data-i18n-key="bookmarkletLink">Save to Read Today</a>
            </div>
        </div>
    </main>
    <script src="../js/theme-switcher.js"></script>
    <script src="../js/i18n.js"></script>
    <script src="../js/lang-switcher.js"></script>
    <script src="../js/settings.js"></script>
</body>
</html>
